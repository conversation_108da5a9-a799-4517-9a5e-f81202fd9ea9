@echo off
echo ========================================
echo Daily Breakout Master Bot - Compilation Test
echo Developed by <PERSON>
echo InnovationX International
echo ========================================
echo.

echo Testing MQ4 file syntax...
echo.

REM Check if MetaEditor is available in common paths
set METAEDITOR=""
if exist "C:\Program Files\MetaTrader 4\metaeditor.exe" set METAEDITOR="C:\Program Files\MetaTrader 4\metaeditor.exe"
if exist "C:\Program Files (x86)\MetaTrader 4\metaeditor.exe" set METAEDITOR="C:\Program Files (x86)\MetaTrader 4\metaeditor.exe"
if exist "%APPDATA%\MetaQuotes\Terminal\*\MQL4\metaeditor.exe" set METAEDITOR="%APPDATA%\MetaQuotes\Terminal\*\MQL4\metaeditor.exe"

if %METAEDITOR%=="" (
    echo MetaEditor not found in common locations.
    echo Please compile manually in MetaEditor:
    echo 1. Open MetaEditor
    echo 2. Open DailyBreakout_MT4.mq4
    echo 3. Press F7 to compile
    echo 4. Check for any compilation errors
    echo.
    echo If compilation is successful, the settings should load properly.
) else (
    echo Found MetaEditor, attempting compilation...
    %METAEDITOR% /compile:"DailyBreakout_MT4.mq4"
    echo.
    echo Compilation attempt completed.
    echo Check the Experts tab in MetaEditor for any errors.
)

echo.
echo ========================================
echo TROUBLESHOOTING TIPS:
echo ========================================
echo.
echo If settings still won't load:
echo 1. Ensure EA is compiled successfully (no errors)
echo 2. Restart MetaTrader 4 completely
echo 3. Clear MetaTrader cache (delete *.ex4 files and recompile)
echo 4. Check if input parameters appear in EA properties
echo 5. Try loading the DailyBreakout_Settings.set file
echo.
echo For technical support:
echo Contact: Brian Alvin Bagorogoza
echo Phone: +49 1521 6294394
echo.
pause
